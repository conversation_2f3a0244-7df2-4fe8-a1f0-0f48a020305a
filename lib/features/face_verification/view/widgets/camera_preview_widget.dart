import 'dart:async';
import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:bloomg_flutter/features/face_verification/bloc/face_video_capture_bloc.dart';
import 'package:bloomg_flutter/features/face_verification/models/face_detection_result.dart';
import 'package:bloomg_flutter/features/face_verification/services/face_detection_service.dart';
import 'package:bloomg_flutter/features/face_verification/view/widgets/face_guide_overlay.dart';
import 'package:bloomg_flutter/shared/constants/logging_constants.dart';
import 'package:bloomg_flutter/shared/services/logger_service.dart';
import 'package:camerawesome/camerawesome_plugin.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';

/// {@template camera_preview_widget}
/// Widget that displays the camera preview with face detection overlay.
///
/// Shows a full-screen camera preview with a face guide overlay
/// and real-time face detection feedback.
/// {@endtemplate}
class CameraPreviewWidget extends StatefulWidget {
  /// {@macro camera_preview_widget}
  const CameraPreviewWidget({super.key});

  @override
  State<CameraPreviewWidget> createState() => _CameraPreviewWidgetState();
}

class _CameraPreviewWidgetState extends State<CameraPreviewWidget> {
  final LoggerService _logger = LoggerService();
  FaceDetectionService? _faceDetectionService;
  Timer? _faceDetectionTimer;
  bool _isProcessingFrame = false;
  int _frameCount = 0; // For frame throttling

  // Camera state for recording control
  bool _isRecordingActive = false;

  @override
  void initState() {
    super.initState();
    _initializeFaceDetectionService();
  }

  @override
  void dispose() {
    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Camera preview widget disposing',
      ),
    );

    // Cancel timer first to stop processing
    _faceDetectionTimer?.cancel();
    _faceDetectionTimer = null;

    // Dispose face detection service
    _faceDetectionService?.dispose();
    _faceDetectionService = null;

    // Clear processing flag
    _isProcessingFrame = false;

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Camera preview widget disposed successfully',
      ),
    );

    super.dispose();
  }

  /// Initializes the face detection service (main thread)
  Future<void> _initializeFaceDetectionService() async {
    try {
      _faceDetectionService = FaceDetectionService();
      await _faceDetectionService!.initialize();

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Face detection service initialized',
          'Main thread processing with frame throttling for optimal '
              'performance',
        ),
      );
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.faceVerificationModule,
          LoggingConstants.criticalError,
          'Face detection service initialization failed: $error',
        ),
        error,
        stackTrace,
      );
      rethrow; // Re-throw to prevent silent failures
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<FaceVideoCaptureBloc, FaceVideoCaptureState>(
      listener: (context, state) {
        _handleRecordingStateChange(state);
      },
      child: BlocBuilder<FaceVideoCaptureBloc, FaceVideoCaptureState>(
        builder: (context, state) {
          return Stack(
            children: [
              // Camera preview background
              _buildCameraPreview(context, state),

              // Face guide overlay
              FaceGuideOverlay(
                currentDetection: state.currentDetection,
                isRecording: state is Recording,
              ),
            ],
          );
        },
      ),
    );
  }

  /// Builds the camera preview with CamerAwesome integration
  Widget _buildCameraPreview(
    BuildContext context,
    FaceVideoCaptureState state,
  ) {
    // Handle different states
    if (state is CameraInitializing) {
      return _buildLoadingState();
    }

    if (state is Error) {
      return _buildErrorState(state.errorMessage ?? 'Camera error occurred');
    }

    // Build CamerAwesome widget for camera ready and recording states
    return _buildCameraAwesome(context, state);
  }

  /// Builds the loading state during camera initialization
  Widget _buildLoadingState() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: const Color(0xFF1A1A1A),
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
            SizedBox(height: 16),
            Text(
              'Initializing Camera...',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Builds the error state when camera fails
  Widget _buildErrorState(String errorMessage) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: const Color(0xFF1A1A1A),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            const Text(
              'Camera Error',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: Text(
                errorMessage,
                textAlign: TextAlign.center,
                style: const TextStyle(
                  color: Colors.white70,
                  fontSize: 14,
                ),
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () {
                context
                    .read<FaceVideoCaptureBloc>()
                    .add(const InitializeCamera());
              },
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  /// Builds the CamerAwesome widget with face detection integration
  Widget _buildCameraAwesome(
    BuildContext context,
    FaceVideoCaptureState state,
  ) {
    return CameraAwesomeBuilder.awesome(
      // Enable video recording while maintaining image analysis
      saveConfig: SaveConfig.photoAndVideo(
        initialCaptureMode: CaptureMode.video,
      ),
      sensorConfig: SensorConfig.single(
        sensor: Sensor.position(SensorPosition.front),
        aspectRatio: CameraAspectRatios.ratio_16_9,
      ),
      onMediaCaptureEvent: _handleMediaCaptureEvent,
      onImageForAnalysis: (analysisImage) async {
        _logger.debug(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'Frame received for analysis',
            'Size: ${analysisImage.width}x${analysisImage.height}, '
                'Format: ${analysisImage.runtimeType}',
          ),
        );
        _processFrameForFaceDetection(analysisImage);
      },
      imageAnalysisConfig: AnalysisConfig(
        androidOptions: const AndroidAnalysisOptions.nv21(
          width: 480,
          // Higher resolution for better face detection
          //Autostart is always true no need to add it here
        ),
        maxFramesPerSecond: 10, // Optimized FPS for face detection
      ),
      theme: AwesomeTheme(
        bottomActionsBackgroundColor: Colors.transparent,
        buttonTheme: AwesomeButtonTheme(
          backgroundColor: Colors.transparent,
          iconSize: 0, // Hide default buttons
        ),
      ),
    );
  }

  /// Handles recording state changes from the BLoC
  void _handleRecordingStateChange(FaceVideoCaptureState state) {
    if (state is Recording && !_isRecordingActive) {
      // Start recording
      _startVideoRecording();
    } else if (state is! Recording && _isRecordingActive) {
      // Stop recording
      _stopVideoRecording();
    }
  }

  /// Starts video recording programmatically
  void _startVideoRecording() {
    if (_isRecordingActive) return;

    setState(() {
      _isRecordingActive = true;
    });

    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Starting video recording from BLoC state change',
      ),
    );
    // Note: For now, we'll use a timer-based approach since CamerAwesome
    // doesn't provide easy programmatic recording control
    // The actual video file will be created when recording stops
  }

  /// Stops video recording programmatically
  void _stopVideoRecording() {
    if (!_isRecordingActive) return;

    setState(() {
      _isRecordingActive = false;
    });

    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Stopping video recording from BLoC state change',
      ),
    );
    // Video recording is handled by CamerAwesome through onMediaCaptureEvent
  }

  /// Handles media capture events from CamerAwesome
  void _handleMediaCaptureEvent(dynamic event) {
    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Media capture event received',
        'Status: ${event.status}, IsVideo: ${event.isVideo}',
      ),
    );

    // Handle video recording events
    if (event.isVideo == true) {
      switch (event.status.toString()) {
        case 'MediaCaptureStatus.capturing':
          _logger.info(
            LoggingConstants.formatMessage(
              LoggingConstants.faceVerificationModule,
              'Video recording started by CamerAwesome',
            ),
          );
        case 'MediaCaptureStatus.success':
          _handleVideoRecordingSuccess(event);
        case 'MediaCaptureStatus.failure':
          _handleVideoRecordingFailure(event);
      }
    }
  }

  /// Handles successful video recording completion
  void _handleVideoRecordingSuccess(dynamic event) {
    event.captureRequest.when(
      single: (single) {
        final videoPath = single.file?.path as String?;
        if (videoPath != null) {
          _logger.info(
            LoggingConstants.formatMessage(
              LoggingConstants.faceVerificationModule,
              'Video recording completed successfully',
              'Path: $videoPath',
            ),
          );

          // Set the actual video path in the repository so it can be used
          // when the BLoC calls stopRecording()
          context.read<FaceVideoCaptureBloc>().add(
                VideoRecordingCompleted(videoPath: videoPath),
              );
        }
      },
      multiple: (multiple) {
        _logger.warning(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'Multiple video capture not supported',
          ),
        );
      },
    );
  }

  /// Handles video recording failure
  void _handleVideoRecordingFailure(dynamic event) {
    _logger.error(
      LoggingConstants.formatError(
        LoggingConstants.faceVerificationModule,
        LoggingConstants.criticalError,
        'Video recording failed: ${event.exception}',
      ),
    );

    // Reset to camera ready state on failure
    context.read<FaceVideoCaptureBloc>().add(
          const ResetCapture(),
        );
  }

  /// Processes camera frames for real-time face detection on main thread
  void _processFrameForFaceDetection(AnalysisImage analysisImage) {
    // CRITICAL FIX: Process every 2nd frame instead of every 3rd for better
    // consistency with reduced buffer size
    _frameCount++;
    if (_frameCount % 2 != 0) {
      return;
    }

    // Skip if already processing a frame, service is null, or widget disposed
    if (_isProcessingFrame || _faceDetectionService == null || !mounted) {
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Frame processing skipped',
          'Processing: $_isProcessingFrame, '
              'Service: ${_faceDetectionService != null}, Mounted: $mounted',
        ),
      );
      return;
    }

    _isProcessingFrame = true;
    final frameStartTime = DateTime.now();

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Frame processing started',
        'Size: ${analysisImage.width}x${analysisImage.height}, '
            'Frame: $_frameCount',
      ),
    );

    // Convert AnalysisImage to InputImage for ML Kit processing
    final conversionStartTime = DateTime.now();
    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Starting InputImage conversion',
        'Frame: $_frameCount, '
            'AnalysisImage type: ${analysisImage.runtimeType}, '
            'Size: ${analysisImage.width}x${analysisImage.height}',
      ),
    );

    final inputImageFuture = _convertAnalysisImageToInputImage(analysisImage);

    inputImageFuture.then((inputImage) {
      final conversionTime =
          DateTime.now().difference(conversionStartTime).inMilliseconds;

      if (inputImage == null) {
        _logger.warning(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'Failed to convert AnalysisImage to InputImage',
            'Frame processing aborted - conversion failed, '
                'Frame: $_frameCount, Type: ${analysisImage.runtimeType}, '
                'Size: ${analysisImage.width}x${analysisImage.height}, '
                'Conversion time: ${conversionTime}ms',
          ),
        );
        _isProcessingFrame = false;

        // Track conversion failure for diagnostics
        _faceDetectionService?.trackConversionFailure();

        // Send a failed detection result to maintain frame buffer consistency
        final failedResult = FaceDetectionResult(
          faceDetected: false,
          faceCount: 0,
          coveragePercentage: 0,
          timestamp: DateTime.now(),
        );

        if (mounted) {
          try {
            // Notify BLoC about conversion failure for tracking
            context
                .read<FaceVideoCaptureBloc>()
                .add(ProcessFrame(failedResult));
          } catch (error) {
            _logger.warning(
              LoggingConstants.formatMessage(
                LoggingConstants.faceVerificationModule,
                'Failed to send conversion failure to BLoC',
                'Error: $error',
              ),
            );
          }
        }
        return;
      }

      // Log successful conversion with timing
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'InputImage conversion successful',
          'Frame: $_frameCount, Conversion time: ${conversionTime}ms, '
              'Size: ${inputImage.metadata?.size}, '
              'Format: ${inputImage.metadata?.format}',
        ),
      );

      // Track successful conversion for diagnostics
      _faceDetectionService?.trackConversionSuccess();

      // Process frame using main thread service with increased timeout
      // CRITICAL FIX: Increase timeout from 500ms to 750ms for slower devices
      final processingFuture =
          _faceDetectionService!.processImage(inputImage).timeout(
        const Duration(
          milliseconds: 750,
        ), // Increased timeout for better Android stability
        onTimeout: () {
          _logger.warning(
            LoggingConstants.formatMessage(
              LoggingConstants.faceVerificationModule,
              'Frame processing timeout on main thread',
              'Processing took longer than 750ms - device may be slow',
            ),
          );
          return null;
        },
      );

      processingFuture.then((result) {
        final processingTime = DateTime.now().difference(frameStartTime);

        if (result != null && mounted) {
          try {
            // Send detection result to BLoC
            context.read<FaceVideoCaptureBloc>().add(ProcessFrame(result));

            _logger.debug(
              LoggingConstants.formatMessage(
                LoggingConstants.faceVerificationModule,
                'ProcessFrame event sent to BLoC',
                'Faces: ${result.faceCount}, Coverage: '
                    '${result.coveragePercentage.toStringAsFixed(1)}%, '
                    'Time: ${processingTime.inMilliseconds}ms',
              ),
            );
          } catch (error) {
            _logger.warning(
              LoggingConstants.formatMessage(
                LoggingConstants.faceVerificationModule,
                'BLoC access failed during frame processing',
                'Error: $error',
              ),
            );
          }
        } else if (result == null) {
          _logger.debug(
            LoggingConstants.formatMessage(
              LoggingConstants.faceVerificationModule,
              'Frame processing returned null result',
              'Time: ${processingTime.inMilliseconds}ms',
            ),
          );
        }
      }).catchError((Object error) {
        final processingTime = DateTime.now().difference(frameStartTime);
        _logger.warning(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'Face detection frame processing failed',
            'Error: $error, Time: ${processingTime.inMilliseconds}ms',
          ),
        );
      }).whenComplete(() {
        _isProcessingFrame = false;
        final totalTime = DateTime.now().difference(frameStartTime);
        _logger.debug(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'Frame processing completed',
            'Total time: ${totalTime.inMilliseconds}ms',
          ),
        );
      });
    }).catchError((Object error) {
      _logger.error(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'AnalysisImage conversion failed',
          'Error: $error',
        ),
      );
      _isProcessingFrame = false;
    });
  }

  /// Converts CamerAwesome AnalysisImage to ML Kit InputImage
  Future<InputImage?> _convertAnalysisImageToInputImage(
    AnalysisImage analysisImage,
  ) async {
    try {
      return analysisImage.when(
        nv21: (nv21Image) {
          _logger.debug(
            LoggingConstants.formatMessage(
              LoggingConstants.faceVerificationModule,
              'Converting NV21 AnalysisImage to InputImage',
              'Size: ${analysisImage.width}x${analysisImage.height}',
            ),
          );

          // CRITICAL FIX: Calculate proper bytesPerRow for NV21 format
          // NV21 Y plane has width stride, UV plane has width stride
          final yPlaneSize = analysisImage.width * analysisImage.height;
          final uvPlaneSize = (analysisImage.width * analysisImage.height) ~/ 2;
          final expectedSize = yPlaneSize + uvPlaneSize;

          _logger.debug(
            LoggingConstants.formatMessage(
              LoggingConstants.faceVerificationModule,
              'NV21 format validation',
              'Expected size: $expectedSize, '
                  'Actual size: ${nv21Image.bytes.length}',
            ),
          );

          return InputImage.fromBytes(
            bytes: nv21Image.bytes,
            metadata: InputImageMetadata(
              size: ui.Size(
                analysisImage.width.toDouble(),
                analysisImage.height.toDouble(),
              ),
              rotation: InputImageRotation.rotation0deg,
              format: InputImageFormat.nv21,
              bytesPerRow: analysisImage.width, // Y plane stride
            ),
          );
        },
        bgra8888: (bgra8888Image) {
          _logger.debug(
            LoggingConstants.formatMessage(
              LoggingConstants.faceVerificationModule,
              'Converting BGRA8888 AnalysisImage to InputImage',
              'Size: ${analysisImage.width}x${analysisImage.height}',
            ),
          );

          return InputImage.fromBytes(
            bytes: bgra8888Image.bytes,
            metadata: InputImageMetadata(
              size: ui.Size(
                analysisImage.width.toDouble(),
                analysisImage.height.toDouble(),
              ),
              rotation: InputImageRotation.rotation0deg,
              format: InputImageFormat.bgra8888,
              bytesPerRow: analysisImage.width * 4,
            ),
          );
        },
        yuv420: (yuv420Image) {
          _logger.debug(
            LoggingConstants.formatMessage(
              LoggingConstants.faceVerificationModule,
              'Converting YUV420 AnalysisImage to InputImage',
              'Size: ${analysisImage.width}x${analysisImage.height}, '
                  'Planes: ${yuv420Image.planes.length}',
            ),
          );

          // CRITICAL FIX: Improved YUV420 plane combination
          final combinedBytes = _combineYUV420PlanesImproved(yuv420Image);

          if (combinedBytes.isEmpty) {
            _logger.warning(
              LoggingConstants.formatMessage(
                LoggingConstants.faceVerificationModule,
                'YUV420 plane combination failed',
                'Unable to combine planes properly',
              ),
            );
            // Return null to indicate conversion failure
            throw Exception('YUV420 plane combination failed');
          }

          return InputImage.fromBytes(
            bytes: combinedBytes,
            metadata: InputImageMetadata(
              size: ui.Size(
                analysisImage.width.toDouble(),
                analysisImage.height.toDouble(),
              ),
              rotation: InputImageRotation.rotation0deg,
              format: InputImageFormat.yuv420,
              bytesPerRow: analysisImage.width,
            ),
          );
        },
      );
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.faceVerificationModule,
          LoggingConstants.recoverableError,
          'AnalysisImage to InputImage conversion failed: $error',
        ),
        error,
        stackTrace,
      );
      return null;
    }
  }

  /// Combines YUV420 planes into a single byte array with improved handling
  Uint8List _combineYUV420PlanesImproved(Yuv420Image yuv420Image) {
    try {
      final planes = yuv420Image.planes;

      if (planes.isEmpty) {
        _logger.warning(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'YUV420 planes empty',
            'No planes available for combination',
          ),
        );
        return Uint8List(0);
      }

      final allBytes = <int>[];

      // Combine all plane bytes with validation
      for (var i = 0; i < planes.length; i++) {
        final plane = planes[i];
        _logger.debug(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'Processing YUV420 plane $i',
            'Bytes length: ${plane.bytes.length}',
          ),
        );
        allBytes.addAll(plane.bytes);
      }

      final result = Uint8List.fromList(allBytes);

      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'YUV420 planes combined successfully',
          'Total bytes: ${result.length}, Planes: ${planes.length}',
        ),
      );

      return result;
    } catch (error) {
      _logger.error(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'YUV420 plane combination error',
          'Error: $error',
        ),
      );
      return Uint8List(0);
    }
  }
}
