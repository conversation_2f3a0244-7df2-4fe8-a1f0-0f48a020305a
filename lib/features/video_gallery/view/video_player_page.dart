import 'dart:io';

import 'package:bloomg_flutter/features/video_gallery/models/video_item.dart';
import 'package:bloomg_flutter/shared/constants/logging_constants.dart';
import 'package:bloomg_flutter/shared/services/logger_service.dart';
import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';

/// {@template video_player_page}
/// A page that displays a video player for playing back recorded videos.
///
/// Features:
/// - Full-screen video playback
/// - Play/pause controls
/// - Progress indicator
/// - Error handling for corrupted videos
/// - Navigation back to gallery
/// {@endtemplate}
class VideoPlayerPage extends StatefulWidget {
  /// {@macro video_player_page}
  const VideoPlayerPage({
    required this.video,
    super.key,
  });

  /// The video item to play
  final VideoItem video;

  @override
  State<VideoPlayerPage> createState() => _VideoPlayerPageState();
}

class _VideoPlayerPageState extends State<VideoPlayerPage> {
  final LoggerService _logger = LoggerService();
  VideoPlayerController? _controller;
  bool _isInitialized = false;
  bool _hasError = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _initializeVideoPlayer();
  }

  @override
  void dispose() {
    _controller?.dispose();
    super.dispose();
  }

  /// Initializes the video player with the provided video file
  Future<void> _initializeVideoPlayer() async {
    try {
      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.videoGalleryModule,
          'Initializing video player',
          'File: ${widget.video.filePath}',
        ),
      );

      final file = File(widget.video.filePath);
      if (!await file.exists()) {
        throw Exception('Video file not found: ${widget.video.filePath}');
      }

      _controller = VideoPlayerController.file(file);
      await _controller!.initialize();

      if (mounted) {
        setState(() {
          _isInitialized = true;
        });

        _logger.info(
          LoggingConstants.formatMessage(
            LoggingConstants.videoGalleryModule,
            'Video player initialized successfully',
            'Duration: ${_controller!.value.duration}',
          ),
        );
      }
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.videoGalleryModule,
          LoggingConstants.criticalError,
          'Failed to initialize video player: $error',
        ),
        error,
        stackTrace,
      );

      if (mounted) {
        setState(() {
          _hasError = true;
          _errorMessage = error.toString();
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        iconTheme: const IconThemeData(color: Colors.white),
        title: Text(
          widget.video.fileName,
          style: const TextStyle(color: Colors.white),
        ),
      ),
      body: _buildVideoPlayer(),
    );
  }

  /// Builds the video player widget based on current state
  Widget _buildVideoPlayer() {
    if (_hasError) {
      return _buildErrorWidget();
    }

    if (!_isInitialized) {
      return _buildLoadingWidget();
    }

    return _buildVideoPlayerWidget();
  }

  /// Builds the loading widget while video is initializing
  Widget _buildLoadingWidget() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(color: Colors.white),
          SizedBox(height: 16),
          Text(
            'Loading video...',
            style: TextStyle(color: Colors.white),
          ),
        ],
      ),
    );
  }

  /// Builds the error widget when video fails to load
  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            color: Colors.red,
            size: 64,
          ),
          const SizedBox(height: 16),
          const Text(
            'Failed to load video',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _errorMessage ?? 'Unknown error',
            style: const TextStyle(color: Colors.white70),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Back to Gallery'),
          ),
        ],
      ),
    );
  }

  /// Builds the main video player widget with controls
  Widget _buildVideoPlayerWidget() {
    return Center(
      child: AspectRatio(
        aspectRatio: _controller!.value.aspectRatio,
        child: Stack(
          children: [
            VideoPlayer(_controller!),
            _buildVideoControls(),
          ],
        ),
      ),
    );
  }

  /// Builds video playback controls overlay
  Widget _buildVideoControls() {
    return Positioned.fill(
      child: GestureDetector(
        onTap: _togglePlayPause,
        child: ColoredBox(
          color: Colors.transparent,
          child: Center(
            child: AnimatedOpacity(
              opacity: _controller!.value.isPlaying ? 0.0 : 1.0,
              duration: const Duration(milliseconds: 300),
              child: Container(
                decoration: const BoxDecoration(
                  color: Colors.black54,
                  shape: BoxShape.circle,
                ),
                padding: const EdgeInsets.all(16),
                child: Icon(
                  _controller!.value.isPlaying ? Icons.pause : Icons.play_arrow,
                  color: Colors.white,
                  size: 48,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// Toggles video play/pause state
  void _togglePlayPause() {
    if (_controller == null) return;

    setState(() {
      if (_controller!.value.isPlaying) {
        _controller!.pause();
        _logger.debug(
          LoggingConstants.formatMessage(
            LoggingConstants.videoGalleryModule,
            'Video paused',
          ),
        );
      } else {
        _controller!.play();
        _logger.debug(
          LoggingConstants.formatMessage(
            LoggingConstants.videoGalleryModule,
            'Video playing',
          ),
        );
      }
    });
  }
}
