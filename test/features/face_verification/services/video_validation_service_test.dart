import 'dart:io';

import 'package:bloomg_flutter/features/face_verification/models/face_detection_result.dart';
import 'package:bloomg_flutter/features/face_verification/services/video_validation_service.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

// Mock classes
class MockFile extends Mock implements File {}

void main() {
  group('[FACE_VERIFICATION] VideoValidationService', () {
    late VideoValidationService service;
    late MockFile mockFile;

    setUp(() {
      mockFile = MockFile();
      service = VideoValidationService(
        fileFactory: (path) => mockFile,
      );
    });

    group('[FACE_VERIFICATION] Action: Video validation', () {
      test('validates video successfully with good quality', () async {
        final detectionResults = _createGoodQualityDetectionResults();
        const recordingDuration = Duration(seconds: 9);
        const videoPath = '/test/path/video.mp4';

        // Mock file operations
        when(() => mockFile.exists()).thenAnswer((_) async => true);
        when(() => mockFile.length()).thenAnswer(
          (_) async => 1024 * 1024,
        ); // 1MB

        final result = await service.validateVideo(
          videoPath: videoPath,
          detectionResults: detectionResults,
          recordingDuration: recordingDuration,
        );

        expect(result.isValid, isTrue);
        expect(result.videoPath, equals(videoPath));
        expect(result.coverageStats.totalFrames, equals(100));
        expect(result.qualityScore, greaterThan(70.0));
      });

      test('fails validation with poor quality', () async {
        final detectionResults = _createPoorQualityDetectionResults();
        const recordingDuration = Duration(seconds: 9);
        const videoPath = '/test/path/video.mp4';

        when(() => mockFile.exists()).thenAnswer((_) async => true);
        when(() => mockFile.length()).thenAnswer((_) async => 1024 * 1024);

        final result = await service.validateVideo(
          videoPath: videoPath,
          detectionResults: detectionResults,
          recordingDuration: recordingDuration,
        );

        expect(result.isValid, isFalse);
        expect(result.reason, contains('Validation failed'));
        expect(result.qualityScore, lessThan(70.0));
      });

      test('fails validation when video file does not exist', () async {
        final detectionResults = _createGoodQualityDetectionResults();
        const recordingDuration = Duration(seconds: 9);
        const videoPath = '/test/path/nonexistent.mp4';

        when(() => mockFile.exists()).thenAnswer((_) async => false);

        final result = await service.validateVideo(
          videoPath: videoPath,
          detectionResults: detectionResults,
          recordingDuration: recordingDuration,
        );

        expect(result.isValid, isFalse);
        expect(result.reason, equals('Video file not found'));
      });

      test('fails validation when video file is empty', () async {
        final detectionResults = _createGoodQualityDetectionResults();
        const recordingDuration = Duration(seconds: 9);
        const videoPath = '/test/path/empty.mp4';

        when(() => mockFile.exists()).thenAnswer((_) async => true);
        when(() => mockFile.length()).thenAnswer((_) async => 0);

        final result = await service.validateVideo(
          videoPath: videoPath,
          detectionResults: detectionResults,
          recordingDuration: recordingDuration,
        );

        expect(result.isValid, isFalse);
        expect(result.reason, equals('Video file is empty'));
      });
    });

    group('[FACE_VERIFICATION] Action: Duration validation', () {
      test('fails validation for too short recording', () async {
        final detectionResults = _createGoodQualityDetectionResults();
        const recordingDuration = Duration(seconds: 5); // Too short
        const videoPath = '/test/path/video.mp4';

        when(() => mockFile.exists()).thenAnswer((_) async => true);
        when(() => mockFile.length()).thenAnswer((_) async => 1024 * 1024);

        final result = await service.validateVideo(
          videoPath: videoPath,
          detectionResults: detectionResults,
          recordingDuration: recordingDuration,
        );

        expect(result.isValid, isFalse);
        expect(result.reason, contains('Recording too short'));
      });

      test('fails validation for too long recording', () async {
        final detectionResults = _createGoodQualityDetectionResults();
        const recordingDuration = Duration(seconds: 15); // Too long
        const videoPath = '/test/path/video.mp4';

        when(() => mockFile.exists()).thenAnswer((_) async => true);
        when(() => mockFile.length()).thenAnswer((_) async => 1024 * 1024);

        final result = await service.validateVideo(
          videoPath: videoPath,
          detectionResults: detectionResults,
          recordingDuration: recordingDuration,
        );

        expect(result.isValid, isFalse);
        expect(result.reason, contains('Recording too long'));
      });

      test('passes validation for correct duration', () async {
        final detectionResults = _createGoodQualityDetectionResults();
        const recordingDuration = Duration(seconds: 9); // Perfect
        const videoPath = '/test/path/video.mp4';

        when(() => mockFile.exists()).thenAnswer((_) async => true);
        when(() => mockFile.length()).thenAnswer((_) async => 1024 * 1024);

        final result = await service.validateVideo(
          videoPath: videoPath,
          detectionResults: detectionResults,
          recordingDuration: recordingDuration,
        );

        expect(result.isValid, isTrue);
      });
    });

    group('[FACE_VERIFICATION] Action: Coverage statistics calculation', () {
      test('calculates coverage stats correctly', () {
        final detectionResults = _createMixedQualityDetectionResults();
        const recordingDuration = Duration(seconds: 9);

        final stats = service.calculateCoverageStatsWithQuality(
          detectionResults,
          recordingDuration,
        );

        expect(stats.totalFrames, equals(detectionResults.length));
        expect(stats.framesWithFace, greaterThan(0));
        expect(stats.framesWithValidCoverage, greaterThan(0));
        expect(stats.averageCoverage, greaterThan(0.0));
        expect(stats.recordingDuration, equals(recordingDuration));
      });

      test('handles empty detection results', () {
        final detectionResults = <FaceDetectionResult>[];
        const recordingDuration = Duration(seconds: 9);

        final stats = service.calculateCoverageStatsWithQuality(
          detectionResults,
          recordingDuration,
        );

        expect(stats.totalFrames, equals(0));
        expect(stats.framesWithFace, equals(0));
        expect(stats.framesWithValidCoverage, equals(0));
        expect(stats.averageCoverage, equals(0.0));
      });
    });

    group('[FACE_VERIFICATION] Action: Error handling', () {
      test('handles validation service errors gracefully', () async {
        final detectionResults = _createGoodQualityDetectionResults();
        const recordingDuration = Duration(seconds: 9);
        const videoPath = '/test/path/video.mp4';

        when(() => mockFile.exists()).thenThrow(Exception('File system error'));

        final result = await service.validateVideo(
          videoPath: videoPath,
          detectionResults: detectionResults,
          recordingDuration: recordingDuration,
        );

        expect(result.isValid, isFalse);
        expect(result.reason, contains('Validation error'));
      });

      test('handles null or invalid detection results', () async {
        final detectionResults = <FaceDetectionResult>[];
        const recordingDuration = Duration(seconds: 9);
        const videoPath = '/test/path/video.mp4';

        when(() => mockFile.exists()).thenAnswer((_) async => true);
        when(() => mockFile.length()).thenAnswer((_) async => 1024 * 1024);

        final result = await service.validateVideo(
          videoPath: videoPath,
          detectionResults: detectionResults,
          recordingDuration: recordingDuration,
        );

        expect(result.isValid, isFalse);
        expect(result.reason, contains('Insufficient frames'));
      });
    });
  });
}

// Helper methods to create test data
List<FaceDetectionResult> _createGoodQualityDetectionResults() {
  return List.generate(100, (index) {
    return FaceDetectionResult(
      faceDetected: true,
      faceCount: 1,
      coveragePercentage: 85.0 + (index % 10), // 85-94%
      timestamp: DateTime.now().add(Duration(milliseconds: index * 100)),
    );
  });
}

List<FaceDetectionResult> _createPoorQualityDetectionResults() {
  return List.generate(100, (index) {
    return FaceDetectionResult(
      faceDetected: index % 3 == 0, // Only 33% detection rate
      faceCount: index % 3 == 0 ? 1 : 0,
      coveragePercentage: index % 3 == 0 ? 45.0 : 0.0, // Poor coverage
      timestamp: DateTime.now().add(Duration(milliseconds: index * 100)),
    );
  });
}

List<FaceDetectionResult> _createMixedQualityDetectionResults() {
  return List.generate(50, (index) {
    final isGoodFrame = index.isEven;
    return FaceDetectionResult(
      faceDetected: isGoodFrame,
      faceCount: isGoodFrame ? 1 : 0,
      coveragePercentage: isGoodFrame ? 80.0 : 30.0,
      timestamp: DateTime.now().add(Duration(milliseconds: index * 100)),
    );
  });
}
