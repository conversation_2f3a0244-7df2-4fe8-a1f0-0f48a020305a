import 'package:bloomg_flutter/features/face_verification/models/face_detection_result.dart';
import 'package:bloomg_flutter/features/face_verification/view/widgets/face_guide_overlay.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../../helpers/helpers.dart';

void main() {
  group('[FACE_VERIFICATION] FaceGuideOverlay Widget Tests', () {
    group('[FACE_VERIFICATION] Action: Basic rendering', () {
      testWidgets('renders without detection result', (tester) async {
        await tester.pumpApp(
          const FaceGuideOverlay(),
        );

        expect(find.byType(FaceGuideOverlay), findsOneWidget);
        expect(find.byType(CustomPaint), findsAtLeastNWidgets(1));
      });

      testWidgets('renders with detection result', (tester) async {
        final detectionResult = FaceDetectionResult(
          faceDetected: true,
          faceCount: 1,
          coveragePercentage: 85,
          timestamp: DateTime.now(),
        );

        await tester.pumpApp(
          FaceGuideOverlay(currentDetection: detectionResult),
        );

        expect(find.byType(FaceGuideOverlay), findsOneWidget);
        expect(find.byType(CustomPaint), findsAtLeastNWidgets(1));
      });

      testWidgets('renders in recording mode', (tester) async {
        final detectionResult = FaceDetectionResult(
          faceDetected: true,
          faceCount: 1,
          coveragePercentage: 85,
          timestamp: DateTime.now(),
        );

        await tester.pumpApp(
          FaceGuideOverlay(
            currentDetection: detectionResult,
            isRecording: true,
          ),
        );

        expect(find.byType(FaceGuideOverlay), findsOneWidget);
        expect(find.byType(CustomPaint), findsAtLeastNWidgets(1));
      });
    });

    group('[FACE_VERIFICATION] Action: Detection feedback display', () {
      testWidgets('shows excellent coverage feedback', (tester) async {
        final detectionResult = FaceDetectionResult(
          faceDetected: true,
          faceCount: 1,
          coveragePercentage: 90, // Excellent coverage
          timestamp: DateTime.now(),
        );

        await tester.pumpApp(
          FaceGuideOverlay(currentDetection: detectionResult),
        );

        // Wait for widget to render
        await tester.pump();

        expect(find.text('Coverage: 90%'), findsOneWidget);
        expect(
          find.text('Perfect! Hold this position for recording'),
          findsOneWidget,
        );
      });

      testWidgets('shows good coverage feedback', (tester) async {
        final detectionResult = FaceDetectionResult(
          faceDetected: true,
          faceCount: 1,
          coveragePercentage: 75, // Good coverage
          timestamp: DateTime.now(),
        );

        await tester.pumpApp(
          FaceGuideOverlay(currentDetection: detectionResult),
        );

        expect(find.text('Coverage: 75%'), findsOneWidget);
        expect(
          find.text('Great! Move slightly closer for optimal quality'),
          findsOneWidget,
        );
      });

      testWidgets('shows moderate coverage feedback', (tester) async {
        final detectionResult = FaceDetectionResult(
          faceDetected: true,
          faceCount: 1,
          coveragePercentage: 55, // Moderate coverage
          timestamp: DateTime.now(),
        );

        await tester.pumpApp(
          FaceGuideOverlay(currentDetection: detectionResult),
        );

        expect(find.text('Coverage: 55%'), findsOneWidget);
        expect(
          find.text('Move closer - face needs to be larger'),
          findsOneWidget,
        );
      });

      testWidgets('shows poor coverage feedback', (tester) async {
        final detectionResult = FaceDetectionResult(
          faceDetected: true,
          faceCount: 1,
          coveragePercentage: 35, // Poor coverage (above 30% threshold)
          timestamp: DateTime.now(),
        );

        await tester.pumpApp(
          FaceGuideOverlay(currentDetection: detectionResult),
        );

        expect(find.text('Coverage: 35%'), findsOneWidget);
        expect(
          find.text('Face too small - move much closer to camera'),
          findsOneWidget,
        );
      });

      testWidgets('shows very poor coverage feedback', (tester) async {
        final detectionResult = FaceDetectionResult(
          faceDetected: true,
          faceCount: 1,
          coveragePercentage: 25, // Very poor coverage (below 30% threshold)
          timestamp: DateTime.now(),
        );

        await tester.pumpApp(
          FaceGuideOverlay(currentDetection: detectionResult),
        );

        expect(find.text('Coverage: 25%'), findsOneWidget);
        expect(
          find.text('Face too distant - move closer to fill guide'),
          findsOneWidget,
        );
      });

      testWidgets('shows no face detected feedback', (tester) async {
        final detectionResult = FaceDetectionResult(
          faceDetected: false,
          faceCount: 0,
          coveragePercentage: 0,
          timestamp: DateTime.now(),
        );

        await tester.pumpApp(
          FaceGuideOverlay(currentDetection: detectionResult),
        );

        expect(
          find.text('Position your face in the center guide'),
          findsOneWidget,
        );
      });

      testWidgets('shows multiple faces detected feedback', (tester) async {
        final detectionResult = FaceDetectionResult(
          faceDetected: true,
          faceCount: 2, // Multiple faces
          coveragePercentage: 60,
          timestamp: DateTime.now(),
        );

        await tester.pumpApp(
          FaceGuideOverlay(currentDetection: detectionResult),
        );

        expect(
          find.text('Multiple faces detected - ensure only one person'),
          findsOneWidget,
        );
      });
    });

    group('[FACE_VERIFICATION] Action: Animation behavior', () {
      testWidgets('animates pulse effect', (tester) async {
        final detectionResult = FaceDetectionResult(
          faceDetected: true,
          faceCount: 1,
          coveragePercentage: 85,
          timestamp: DateTime.now(),
        );

        await tester.pumpApp(
          FaceGuideOverlay(currentDetection: detectionResult),
        );

        // Initial state
        expect(find.byType(AnimatedBuilder), findsWidgets);

        // Advance animation
        await tester.pump(const Duration(milliseconds: 500));
        await tester.pump(const Duration(milliseconds: 500));

        // Animation should be running
        expect(find.byType(AnimatedBuilder), findsWidgets);
      });

      testWidgets('changes color based on detection quality', (tester) async {
        // Test excellent quality (should be green)
        final excellentDetection = FaceDetectionResult(
          faceDetected: true,
          faceCount: 1,
          coveragePercentage: 90,
          timestamp: DateTime.now(),
        );

        await tester.pumpApp(
          FaceGuideOverlay(currentDetection: excellentDetection),
        );

        await tester.pump(const Duration(milliseconds: 100));

        // Widget should render with green color indication
        expect(find.byType(CustomPaint), findsAtLeastNWidgets(1));
      });

      testWidgets('handles recording mode visual changes', (tester) async {
        final detectionResult = FaceDetectionResult(
          faceDetected: true,
          faceCount: 1,
          coveragePercentage: 85,
          timestamp: DateTime.now(),
        );

        await tester.pumpApp(
          FaceGuideOverlay(
            currentDetection: detectionResult,
            isRecording: true,
          ),
        );

        // Recording mode should show different visual styling
        expect(find.byType(CustomPaint), findsAtLeastNWidgets(1));
        expect(find.byType(AnimatedBuilder), findsWidgets);
      });
    });

    group('[FACE_VERIFICATION] Action: Layout and positioning', () {
      testWidgets('adapts to different screen sizes', (tester) async {
        final detectionResult = FaceDetectionResult(
          faceDetected: true,
          faceCount: 1,
          coveragePercentage: 85,
          timestamp: DateTime.now(),
        );

        // Test with small screen
        await tester.binding.setSurfaceSize(const Size(400, 600));
        await tester.pumpApp(
          FaceGuideOverlay(currentDetection: detectionResult),
        );

        expect(find.byType(LayoutBuilder), findsOneWidget);

        // Test with large screen
        await tester.binding.setSurfaceSize(const Size(800, 1200));
        await tester.pump();

        expect(find.byType(LayoutBuilder), findsOneWidget);
      });

      testWidgets('positions feedback elements correctly', (tester) async {
        final detectionResult = FaceDetectionResult(
          faceDetected: true,
          faceCount: 1,
          coveragePercentage: 85,
          timestamp: DateTime.now(),
        );

        await tester.pumpApp(
          FaceGuideOverlay(currentDetection: detectionResult),
        );

        // Check that feedback elements are positioned within the widget
        expect(find.byType(Stack), findsOneWidget);
        expect(find.byType(Positioned), findsWidgets);
      });
    });

    group('[FACE_VERIFICATION] Action: Edge cases', () {
      testWidgets('handles null detection result gracefully', (tester) async {
        await tester.pumpApp(
          const FaceGuideOverlay(),
        );

        expect(find.byType(FaceGuideOverlay), findsOneWidget);
        expect(find.byType(CustomPaint), findsAtLeastNWidgets(1));
        // Should not show detection feedback
        expect(
          find.text('Position your face in the center guide'),
          findsNothing,
        );
      });

      testWidgets('handles extreme coverage values', (tester) async {
        // Test 0% coverage
        final zeroDetection = FaceDetectionResult(
          faceDetected: false,
          faceCount: 0,
          coveragePercentage: 0,
          timestamp: DateTime.now(),
        );

        await tester.pumpApp(
          FaceGuideOverlay(currentDetection: zeroDetection),
        );

        expect(
          find.text('Position your face in the center guide'),
          findsOneWidget,
        );

        // Test 100% coverage
        final perfectDetection = FaceDetectionResult(
          faceDetected: true,
          faceCount: 1,
          coveragePercentage: 100,
          timestamp: DateTime.now(),
        );

        await tester.pumpApp(
          FaceGuideOverlay(currentDetection: perfectDetection),
        );

        expect(find.text('Coverage: 100%'), findsOneWidget);
        expect(
          find.text('Perfect! Hold this position for recording'),
          findsOneWidget,
        );
      });

      testWidgets('handles rapid detection updates', (tester) async {
        final initialDetection = FaceDetectionResult(
          faceDetected: true,
          faceCount: 1,
          coveragePercentage: 50,
          timestamp: DateTime.now(),
        );

        await tester.pumpApp(
          FaceGuideOverlay(currentDetection: initialDetection),
        );

        expect(find.text('Coverage: 50%'), findsOneWidget);

        // Update with new detection
        final updatedDetection = FaceDetectionResult(
          faceDetected: true,
          faceCount: 1,
          coveragePercentage: 85,
          timestamp: DateTime.now(),
        );

        await tester.pumpApp(
          FaceGuideOverlay(currentDetection: updatedDetection),
        );

        expect(find.text('Coverage: 85%'), findsOneWidget);
      });
    });
  });
}
