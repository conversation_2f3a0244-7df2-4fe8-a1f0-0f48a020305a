import 'dart:io';

import 'package:bloomg_flutter/features/video_gallery/models/gallery_filter.dart';
import 'package:bloomg_flutter/features/video_gallery/models/video_item.dart';
import 'package:bloomg_flutter/features/video_gallery/services/video_gallery_service.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

// Mocking external dependencies
class MockDirectory extends Mock implements Directory {}

class MockFile extends Mock implements File {}

class MockFileSystemEntity extends Mock implements FileSystemEntity {}

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  // Mock platform channels for path_provider
  setUpAll(() {
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
        .setMockMethodCallHandler(
      const MethodChannel('plugins.flutter.io/path_provider'),
      (MethodCall methodCall) async {
        switch (methodCall.method) {
          case 'getApplicationDocumentsDirectory':
            return '/mock/documents/directory';
          case 'getTemporaryDirectory':
            return '/mock/temp/directory';
          default:
            return null;
        }
      },
    );
  });

  tearDownAll(() {
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
        .setMockMethodCallHandler(
      const MethodChannel('plugins.flutter.io/path_provider'),
      null,
    );
  });

  group('VideoGalleryService', () {
    late VideoGalleryService service;
    late List<VideoItem> sampleVideos;

    setUpAll(() {
      // Register fallback values for any type that might be used
      registerFallbackValue(DateTime.now());
      registerFallbackValue(const Duration(seconds: 1));
    });

    setUp(() {
      service = VideoGalleryService();

      sampleVideos = [
        VideoItem(
          id: 'video-1',
          fileName: 'face_verification_2024-01-15_10-30-45.mp4',
          filePath: '/path/to/video1.mp4',
          fileSize: 1024000,
          createdAt: DateTime(2024, 1, 15, 10, 30, 45),
          duration: const Duration(seconds: 9),
          qualityScore: 85.5,
        ),
        VideoItem(
          id: 'video-2',
          fileName: 'face_verification_2024-01-14_09-15-30.mp4',
          filePath: '/path/to/video2.mp4',
          fileSize: 2048000,
          createdAt: DateTime(2024, 1, 14, 9, 15, 30),
          duration: const Duration(seconds: 9),
          qualityScore: 92,
        ),
        VideoItem(
          id: 'video-3',
          fileName: 'face_verification_2024-01-16_14-45-20.mp4',
          filePath: '/path/to/video3.mp4',
          fileSize: 1536000,
          createdAt: DateTime(2024, 1, 16, 14, 45, 20),
          duration: const Duration(seconds: 9),
          qualityScore: 78.3,
        ),
      ];
    });

    group('filterVideos', () {
      test('should return all videos with default filter', () {
        const filter = GalleryFilter();

        final result = service.filterVideos(sampleVideos, filter);

        expect(result.length, 3);
        // Should be sorted by date descending (default)
        expect(result[0].id, 'video-3'); // 2024-01-16
        expect(result[1].id, 'video-1'); // 2024-01-15
        expect(result[2].id, 'video-2'); // 2024-01-14
      });

      test('should sort by date ascending', () {
        const filter = GalleryFilter(sortBy: GallerySortBy.dateAscending);

        final result = service.filterVideos(sampleVideos, filter);

        expect(result.length, 3);
        expect(result[0].id, 'video-2'); // 2024-01-14
        expect(result[1].id, 'video-1'); // 2024-01-15
        expect(result[2].id, 'video-3'); // 2024-01-16
      });

      test('should sort by quality descending', () {
        const filter = GalleryFilter(sortBy: GallerySortBy.qualityDescending);

        final result = service.filterVideos(sampleVideos, filter);

        expect(result.length, 3);
        expect(result[0].qualityScore, 92.0); // video-2
        expect(result[1].qualityScore, 85.5); // video-1
        expect(result[2].qualityScore, 78.3); // video-3
      });

      test('should sort by quality ascending', () {
        const filter = GalleryFilter(sortBy: GallerySortBy.qualityAscending);

        final result = service.filterVideos(sampleVideos, filter);

        expect(result.length, 3);
        expect(result[0].qualityScore, 78.3); // video-3
        expect(result[1].qualityScore, 85.5); // video-1
        expect(result[2].qualityScore, 92.0); // video-2
      });

      test('should sort by file size descending', () {
        const filter = GalleryFilter(sortBy: GallerySortBy.sizeDescending);

        final result = service.filterVideos(sampleVideos, filter);

        expect(result.length, 3);
        expect(result[0].fileSize, 2048000); // video-2
        expect(result[1].fileSize, 1536000); // video-3
        expect(result[2].fileSize, 1024000); // video-1
      });

      test('should sort by file size ascending', () {
        const filter = GalleryFilter(sortBy: GallerySortBy.sizeAscending);

        final result = service.filterVideos(sampleVideos, filter);

        expect(result.length, 3);
        expect(result[0].fileSize, 1024000); // video-1
        expect(result[1].fileSize, 1536000); // video-3
        expect(result[2].fileSize, 2048000); // video-2
      });

      test('should filter by minimum quality score', () {
        const filter = GalleryFilter(minQualityScore: 80);

        final result = service.filterVideos(sampleVideos, filter);

        expect(result.length, 2);
        expect(result.every((video) => video.qualityScore >= 80), true);
        expect(result.any((video) => video.id == 'video-1'), true); // 85.5
        expect(result.any((video) => video.id == 'video-2'), true); // 92
        expect(result.any((video) => video.id == 'video-3'), false); // 78.3
      });

      test('should filter by very high quality score', () {
        const filter = GalleryFilter(minQualityScore: 90);

        final result = service.filterVideos(sampleVideos, filter);

        expect(result.length, 1);
        expect(result.first.id, 'video-2'); // Only video with 92 quality
      });

      test('should return empty list when quality threshold too high', () {
        const filter = GalleryFilter(minQualityScore: 95);

        final result = service.filterVideos(sampleVideos, filter);

        expect(result, isEmpty);
      });

      test('should filter by date range', () {
        final dateRange = DateRange(
          start: DateTime(2024, 1, 14),
          end: DateTime(2024, 1, 15),
        );
        final filter = GalleryFilter(dateRange: dateRange);

        final result = service.filterVideos(sampleVideos, filter);

        expect(result.length, 2);
        expect(
          result.any((video) => video.id == 'video-1'),
          true,
        ); // 2024-01-15
        expect(
          result.any((video) => video.id == 'video-2'),
          true,
        ); // 2024-01-14
        expect(
          result.any((video) => video.id == 'video-3'),
          false,
        ); // 2024-01-16
      });

      test('should filter by single day date range', () {
        final dateRange = DateRange(
          start: DateTime(2024, 1, 15),
          end: DateTime(2024, 1, 15),
        );
        final filter = GalleryFilter(dateRange: dateRange);

        final result = service.filterVideos(sampleVideos, filter);

        expect(result.length, 1);
        expect(result.first.id, 'video-1'); // Only video from 2024-01-15
      });

      test('should apply multiple filters simultaneously', () {
        final dateRange = DateRange(
          start: DateTime(2024, 1, 14),
          end: DateTime(2024, 1, 16),
        );
        final filter = GalleryFilter(
          sortBy: GallerySortBy.qualityDescending,
          minQualityScore: 80,
          dateRange: dateRange,
        );

        final result = service.filterVideos(sampleVideos, filter);

        expect(result.length, 2);
        // Should be filtered by quality >= 80 and sorted by quality desc
        expect(result[0].qualityScore, 92.0); // video-2 (highest quality)
        expect(result[1].qualityScore, 85.5); // video-1 (second highest)
        // video-3 should be excluded due to quality < 80
        expect(result.any((video) => video.id == 'video-3'), false);
      });

      test('should handle empty video list', () {
        const filter = GalleryFilter();

        final result = service.filterVideos([], filter);

        expect(result, isEmpty);
      });

      test('should handle edge case with zero quality score filter', () {
        const filter = GalleryFilter(minQualityScore: 0);

        final result = service.filterVideos(sampleVideos, filter);

        expect(result.length, 3); // All videos should pass
        expect(result.every((video) => video.qualityScore >= 0), true);
      });

      test('should handle negative quality score filter', () {
        const filter = GalleryFilter(minQualityScore: -10);

        final result = service.filterVideos(sampleVideos, filter);

        expect(result.length, 3); // All videos should pass
      });

      test('should preserve original list when filtering', () {
        const filter = GalleryFilter(minQualityScore: 90);

        final originalLength = sampleVideos.length;
        service.filterVideos(sampleVideos, filter);

        // Original list should remain unchanged
        expect(sampleVideos.length, originalLength);
        expect(sampleVideos[0].id, 'video-1');
      });

      test('should handle videos with same quality scores', () {
        final sameQualityVideos = [
          sampleVideos[0].copyWith(qualityScore: 85),
          sampleVideos[1].copyWith(qualityScore: 85),
          sampleVideos[2].copyWith(qualityScore: 85),
        ];

        const filter = GalleryFilter(sortBy: GallerySortBy.qualityDescending);

        final result = service.filterVideos(sameQualityVideos, filter);

        expect(result.length, 3);
        expect(result.every((video) => video.qualityScore == 85.0), true);
      });

      test('should handle videos with same file sizes', () {
        final sameSizeVideos = [
          sampleVideos[0].copyWith(fileSize: 1024000),
          sampleVideos[1].copyWith(fileSize: 1024000),
          sampleVideos[2].copyWith(fileSize: 1024000),
        ];

        const filter = GalleryFilter(sortBy: GallerySortBy.sizeDescending);

        final result = service.filterVideos(sameSizeVideos, filter);

        expect(result.length, 3);
        expect(result.every((video) => video.fileSize == 1024000), true);
      });

      test('should handle videos with same creation dates', () {
        final sameDate = DateTime(2024, 1, 15);
        final sameDateVideos = [
          sampleVideos[0].copyWith(createdAt: sameDate),
          sampleVideos[1].copyWith(createdAt: sameDate),
          sampleVideos[2].copyWith(createdAt: sameDate),
        ];

        const filter = GalleryFilter();

        final result = service.filterVideos(sameDateVideos, filter);

        expect(result.length, 3);
        expect(result.every((video) => video.createdAt == sameDate), true);
      });
    });

    group('deleteVideo', () {
      test('should delete video file successfully', () async {
        final video = sampleVideos.first;
        final mockFile = MockFile();

        // Mock File constructor and exists/delete methods
        when(mockFile.exists).thenAnswer((_) async => true);
        when(mockFile.delete).thenAnswer((_) async => mockFile);

        // Note: In a real implementation, we'd need to mock the File
        // constructor. For this test, we're testing the logic rather than
        // actual file operations

        // Since we can't easily mock File constructor in this test setup,
        // we'll test the method indirectly by checking it doesn't throw
        expect(() => service.deleteVideo(video), returnsNormally);
      });

      test('should handle video file that does not exist', () async {
        final video = sampleVideos.first;

        // This test verifies the method handles non-existent files gracefully
        expect(() => service.deleteVideo(video), returnsNormally);
      });

      test('should delete thumbnail file if it exists', () async {
        final video = sampleVideos.first.copyWith(
          thumbnailPath: '/path/to/thumbnail.jpg',
        );

        // Test that the method handles thumbnail deletion
        expect(() => service.deleteVideo(video), returnsNormally);
      });

      test('should handle deletion of video without thumbnail', () async {
        final video = sampleVideos.first; // No thumbnail path

        expect(() => service.deleteVideo(video), returnsNormally);
      });
    });

    group('generateThumbnail', () {
      test('should handle thumbnail generation', () async {
        final video = sampleVideos.first;

        // Since VideoThumbnail.thumbnailFile is a static method that's hard
        // to mock,
        // we'll test that the method handles the call gracefully
        expect(() => service.generateThumbnail(video), returnsNormally);
      });

      test('should handle thumbnail generation failure', () async {
        final video = sampleVideos.first;

        // Test that the method handles failures gracefully
        expect(() => service.generateThumbnail(video), returnsNormally);
      });
    });

    group('getStorageStats', () {
      test('should handle storage stats calculation', () async {
        // Since this method depends on file system operations,
        // we'll test that it completes without throwing
        expect(() => service.getStorageStats(), returnsNormally);
      });
    });

    group('Edge cases and error handling', () {
      test('should handle null or empty filter gracefully', () {
        // Test with a filter that has all null values
        const filter = GalleryFilter();

        expect(
          () => service.filterVideos(sampleVideos, filter),
          returnsNormally,
        );
      });

      test('should handle very large video lists', () {
        // Create a large list of videos
        final largeVideoList = List.generate(
          1000,
          (index) => VideoItem(
            id: 'video-$index',
            fileName: 'video_$index.mp4',
            filePath: '/path/to/video$index.mp4',
            fileSize: 1024000 + index,
            createdAt: DateTime(2024).add(Duration(days: index % 30)),
            duration: Duration(seconds: 9 + index % 10),
            qualityScore: (index % 100).toDouble(),
          ),
        );

        const filter = GalleryFilter(minQualityScore: 50);

        expect(
          () => service.filterVideos(largeVideoList, filter),
          returnsNormally,
        );

        final result = service.filterVideos(largeVideoList, filter);
        expect(result.every((video) => video.qualityScore >= 50), true);
      });

      test('should handle extreme date ranges', () {
        final veryOldDate = DateTime(1900);
        final veryFutureDate = DateTime(2100, 12, 31);

        final dateRange = DateRange(start: veryOldDate, end: veryFutureDate);
        final filter = GalleryFilter(dateRange: dateRange);

        expect(
          () => service.filterVideos(sampleVideos, filter),
          returnsNormally,
        );

        final result = service.filterVideos(sampleVideos, filter);
        expect(result.length, 3); // All videos should be included
      });

      test('should handle very strict quality filters', () {
        const filter = GalleryFilter(minQualityScore: 99.9);

        final result = service.filterVideos(sampleVideos, filter);
        expect(result, isEmpty);
      });

      test('should handle zero duration videos', () {
        final videosWithZeroDuration = sampleVideos
            .map(
              (video) => video.copyWith(duration: Duration.zero),
            )
            .toList();

        const filter = GalleryFilter();

        expect(
          () => service.filterVideos(videosWithZeroDuration, filter),
          returnsNormally,
        );

        final result = service.filterVideos(videosWithZeroDuration, filter);
        expect(result.length, 3);
      });

      test('should handle videos with zero file size', () {
        final videosWithZeroSize = sampleVideos
            .map(
              (video) => video.copyWith(fileSize: 0),
            )
            .toList();

        const filter = GalleryFilter(sortBy: GallerySortBy.sizeDescending);

        expect(
          () => service.filterVideos(videosWithZeroSize, filter),
          returnsNormally,
        );

        final result = service.filterVideos(videosWithZeroSize, filter);
        expect(result.length, 3);
        expect(result.every((video) => video.fileSize == 0), true);
      });

      test('should handle videos with negative quality scores', () {
        final videosWithNegativeQuality = sampleVideos
            .map(
              (video) => video.copyWith(qualityScore: -10),
            )
            .toList();

        const filter = GalleryFilter(sortBy: GallerySortBy.qualityDescending);

        expect(
          () => service.filterVideos(videosWithNegativeQuality, filter),
          returnsNormally,
        );

        final result = service.filterVideos(videosWithNegativeQuality, filter);
        expect(result.length, 3);
        expect(result.every((video) => video.qualityScore == -10.0), true);
      });
    });

    group('Performance tests', () {
      test('should filter large datasets efficiently', () {
        // Create a moderately large dataset
        final largeVideoList = List.generate(
          500,
          (index) => VideoItem(
            id: 'video-$index',
            fileName: 'video_$index.mp4',
            filePath: '/path/to/video$index.mp4',
            fileSize: 1024000 + (index * 1000),
            createdAt: DateTime(2024).add(Duration(hours: index)),
            duration: Duration(seconds: 10 + (index % 20)),
            qualityScore: 50.0 + (index % 50),
          ),
        );

        const filter = GalleryFilter(
          sortBy: GallerySortBy.qualityDescending,
          minQualityScore: 75,
        );

        final stopwatch = Stopwatch()..start();
        final result = service.filterVideos(largeVideoList, filter);
        stopwatch.stop();

        // Verify filtering worked correctly
        expect(result.every((video) => video.qualityScore >= 75), true);

        // Verify sorting worked correctly
        for (var i = 0; i < result.length - 1; i++) {
          expect(result[i].qualityScore >= result[i + 1].qualityScore, true);
        }

        // Performance check - should complete in reasonable time (< 100ms)
        expect(stopwatch.elapsedMilliseconds, lessThan(100));
      });
    });
  });
}
