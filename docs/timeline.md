# Bloomg Flutter - Project Timeline

## Architectural Decision Records (ADRs)

---

## **2025-01-26: Firebase Storage Integration Architecture Decision**

### **ADR: Video Storage Architecture with Firebase Storage and Firestore Mirroring**
- **Decision**: Store videos in Firebase Storage with metadata mirrored in Firestore
- **Why**: 
  - **Scalability**: Firebase Storage provides robust video file storage with CDN capabilities
  - **Performance**: Videos stored in Storage with fast metadata queries via Firestore
  - **Cost Optimization**: Storage pricing more efficient for large video files vs Firestore documents
  - **Security**: Fine-grained access control through Firebase Security Rules
  - **Integration**: Seamless integration with existing Firebase Auth and Firestore infrastructure
- **How**: 
  - Videos uploaded to Firebase Storage with structured naming convention
  - Metadata (timestamps, quality scores, user associations) stored in Firestore
  - Dual-storage approach enables fast queries while maintaining video file integrity
  - Existing local storage will be migrated to cloud storage for cross-device accessibility
- **Technical Implementation**:
  - Firebase Storage bucket: `/face_verification_videos/{userId}/{videoId}.mp4`
  - Firestore collection: `/users/{userId}/verificationVideos/{videoId}`
  - Metadata includes: `fileName`, `uploadTimestamp`, `qualityScore`, `thumbnailUrl`, `storageRef`
- **Migration Strategy**: Local videos will be uploaded to Firebase Storage with metadata sync
- **Impact**: Enables cross-device video access, better performance, and centralized video management

---

*Last Updated: 2025-01-26 - Firebase Storage integration architecture decision*
